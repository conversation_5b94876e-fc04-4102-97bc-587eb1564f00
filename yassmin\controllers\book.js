import Book from "../models/book.js";
import mongoose from "mongoose";

export const createBook = async (req, res) => {
  try {
    const {
      title,
      author,
      description,
      category,
      price,
      stock,
      format,
      coverImage,
      publishedDate,
    } = req.body;

    // Validate required fields
    if (
      !title ||
      !author ||
      !description ||
      !category ||
      price == null ||
      stock == null
    ) {
      return res
        .status(400)
        .json({
          error: "All required fields must be filled",
          required: ["title", "author", "description", "category", "price", "stock"]
        });
    }

    // Validate price and stock are positive numbers
    if (price < 0) {
      return res.status(400).json({ error: "Price cannot be negative" });
    }

    if (stock < 0) {
      return res.status(400).json({ error: "Stock cannot be negative" });
    }

    const newBook = new Book({
      title,
      author,
      description,
      category,
      price,
      stock,
      format,
      coverImage,
      publishedDate,
    });

    const savedBook = await newBook.save();
    res.status(201).json({
      message: "Book created successfully",
      book: savedBook
    });
  } catch (err) {
    console.error("Error creating book:", err.message);

    // Handle validation errors
    if (err.name === 'ValidationError') {
      const errors = Object.values(err.errors).map(e => e.message);
      return res.status(400).json({
        error: "Validation failed",
        details: errors
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
};

export const getAllBooks = async (req, res) => {
  try {
    const books = await Book.find();
    res.json(books);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getBookById = async (req, res) => {
  const id = req.params.id;
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid book ID" });
  }

  try {
    const book = await Book.findById(id);
    if (book == null) return res.status(404).json({ error: "Book not found" });
    res.json(book);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const updateBook = async (req, res) => {
  const id = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid book ID" });
  }

  try {
    const {
      title,
      author,
      description,
      category,
      price,
      stock,
      format,
      coverImage,
      publishedDate,
    } = req.body;

    // Validate price and stock if provided
    if (price != null && price < 0) {
      return res.status(400).json({ error: "Price cannot be negative" });
    }

    if (stock != null && stock < 0) {
      return res.status(400).json({ error: "Stock cannot be negative" });
    }

    const updateData = {};
    if (title) updateData.title = title;
    if (author) updateData.author = author;
    if (description) updateData.description = description;
    if (category) updateData.category = category;
    if (price != null) updateData.price = price;
    if (stock != null) updateData.stock = stock;
    if (format) updateData.format = format;
    if (coverImage) updateData.coverImage = coverImage;
    if (publishedDate) updateData.publishedDate = publishedDate;

    const updatedBook = await Book.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedBook) {
      return res.status(404).json({ error: "Book not found" });
    }

    res.json({
      message: "Book updated successfully",
      book: updatedBook
    });
  } catch (err) {
    console.error("Error updating book:", err.message);

    if (err.name === 'ValidationError') {
      const errors = Object.values(err.errors).map(e => e.message);
      return res.status(400).json({
        error: "Validation failed",
        details: errors
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
};

export const deleteBook = async (req, res) => {
  const id = req.params.id;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid book ID" });
  }

  try {
    const deletedBook = await Book.findByIdAndDelete(id);
    if (deletedBook == null)
      return res.status(404).json({ error: "Book not found" });
    res.json({ message: "Book deleted successfully" });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
