import mongoose from "mongoose";

const bookSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Book title is required"],
      trim: true,
    },
    author: {
      type: String,
      required: [true, "Author is required"],
      trim: true,
    },
    description: {
      type: String,
      required: [true, "Description is required"],
    },
    category: {
      type: String,
      required: [true, "Category is required"],
      enum: {
        values: [
          "Fiction",
          "Non-fiction",
          "Science",
          "Biography",
          "Fantasy",
          "Romance",
          "Mystery",
          "History",
          "Horror",
          "Self-improvement",
          "Comics",
          "Children",
          "Technology",
          "Business",
        ],
        message: "Category must be one of the predefined values",
      },
      trim: true,
    },
    price: {
      type: Number,
      required: [true, "Price is required"],
      min: [0, "Price cannot be negative"],
    },
    stock: {
      type: Number,
      required: [true, "Stock is required"],
      min: [0, "Stock cannot be negative"],
    },
    format: {
      type: String,
      enum: ["paperback", "hardcover", "ebook"],
      default: "paperback",
    },
    coverImage: {
      type: String,
      default: "img/book-placeholder.png",
    },
    publishedDate: {
      type: Date,
    },
  },
  { timestamps: true }
);

const Book = mongoose.model("Book", bookSchema);

export default Book;
