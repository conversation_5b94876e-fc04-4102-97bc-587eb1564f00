<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>Index - BookLanding Bootstrap Template</title>
    <%- include ("./partials/css-links.ejs") %>
  </head>

  <body class="index-page">
    <%- include ("./partials/nav.ejs") %>
    <main class="main">
      <section
        id="related-books"
        class="related-books section light-background"
      >
        <div class="container section-title aos-init mt-5" data-aos="fade-up">
          <h2>More Books by the Author</h2>
          <p>
            Necessitatibus eius consequatur ex aliquid fuga eum quidem sint
            consectetur velit
          </p>
        </div>
        <div class="container aos-init" data-aos="fade-up" data-aos-delay="100">
          <div class="row gy-4">
            <% if (books.length == 0){ %>
            <div class="d-flex align-items-center justify-content-center">
              <strong>No books available</strong>
            </div>
            <% }else{ books.forEach(book => { %>
            <div
              class="col-lg-4 col-md-6 aos-init"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              <div class="related-book-card">
                <a href="/books/<%=book._id%>">
                  <div class="book-image">
                    <img
                      src="<%=book.coverImage%>"
                      alt="Book Cover"
                      class="img-fluid"
                    />
                    <div class="book-category"><%=book.category%></div>
                  </div>
                </a>
                <div class="book-info">
                  <h3><%=book.title%></h3>
                  <div class="book-meta">
                    <span
                      ><i class="bi bi-calendar3"></i>
                      <%=book.publishedDate%></span
                    >
                    <span class="text-nowrap"> <%=book.price%> EGP </span>
                  </div>
                  <p><%=book.description%></p>
                  <div class="book-actions float-end mb-4">
                    <button class="btn-purchase btn">
                      <i class="bi bi-heart fs-5"></i>
                    </button>

                    <% let isInCart = false; if (user && user.cart && user.cart.items) {
                    isInCart = user.cart.items.some(item =>
                    item.book._id.toString() === book._id.toString()); } %> <%
                    if (!user) { %>
                    <!-- User not logged in -->
                    <button
                      class="btn-purchase btn add-to-cart-btn"
                      data-bookId="<%= book._id %>"
                    >
                      <i class="bi bi-bag-plus fs-5"></i>
                    </button>
                    <% } else if (user && !isInCart) { %>
                    <!-- User logged in but book NOT in cart -->
                    <button
                      class="btn-purchase btn add-to-cart-btn"
                      data-bookId="<%= book._id %>"
                    >
                      <i class="bi bi-bag-plus fs-5"></i>
                    </button>
                    <% } else { %>
                    <!-- User logged in and book IS in cart -->
                    <button
                      class="btn-purchase btn"
                      style="background-color: green"
                    >
                      <i class="bi bi-bag-check fs-5"></i>
                    </button>
                    <% } %>
                  </div>
                </div>
              </div>
            </div>
            <% })} %>
          </div>

          <script>
            document.addEventListener("DOMContentLoaded", () => {
              // Now this selector will correctly find the buttons
              const cartButtons = document.querySelectorAll(".add-to-cart-btn");

              cartButtons.forEach((button) => {
                button.addEventListener("click", async () => {
                  const bookId = button.getAttribute("data-bookId");

                  // Disable button immediately to prevent multiple clicks
                  button.disabled = true;

                  try {
                    const res = await fetch("/api/user/cart/", {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify({ bookId: bookId, quantity: 1 }),
                    });

                    const data = await res.json();

                    if (data.error) {
                      showToast({
                        text: data.error,
                        type: "error",
                      });
                    } else {
                      showToast({
                        text: data.message,
                        type: "success", // fixed typo from 'sucess' to 'success'
                      });
                      setTimeout(() => {
                        location.reload();
                      }, 1000);
                    }
                  } catch (error) {
                    console.error("Fetch error:", error);
                    showToast({
                      text: "An error occurred while adding the book to the cart.",
                      type: "error",
                    });
                  }
                });
              });
            });
          </script>

          <% if (books.length != 0){ %>
          <div class="row mt-3">
            <div
              class="col-12 text-center aos-init"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              <div class="coming-soon">
                <div class="coming-soon-badge">Coming Soon</div>
                <div class="row align-items-center">
                  <div class="col-md-4">
                    <div class="upcoming-book-image">
                      <img
                        src="img/book-square-1.webp"
                        alt="Upcoming Book"
                        class="img-fluid"
                      />
                    </div>
                  </div>
                  <div class="col-md-8">
                    <div class="upcoming-book-info">
                      <h3>Elit Eget Tincidunt</h3>
                      <p class="release-date">
                        Expected Release: September 2025
                      </p>
                      <p class="description">
                        Nulla quis lorem ut libero malesuada feugiat. Vivamus
                        magna justo, lacinia eget consectetur sed, convallis at
                        tellus. Curabitur non nulla sit amet nisl tempus
                        convallis quis ac lectus.
                      </p>
                      <a
                        href="https://bootstrapmade.com/content/demo/BookLanding/#"
                        class="btn-notify"
                        >Get Notified on Release</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <% }%>
        </div>
      </section>
    </main>
    <%- include ("./partials/footer.ejs") %> <%- include
    ("./partials/js-scripts.ejs") %>
  </body>
</html>
