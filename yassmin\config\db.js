import mongoose from "mongoose";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const connectDb = async () => {
  const mongoUrl = process.env.MONGO_DB_URL;

  if (!mongoUrl) {
    console.error("MONGO_DB_URL environment variable is not defined");
    return;
  }

  mongoose
    .connect(mongoUrl)
    .then(() => {
      console.log("Connected to MongoDB");
    })
    .catch((err) => {
      console.error("Error connecting to MongoDB", err);
    });
};


export default connectDb;