import mongoose from "mongoose";
import Order from "../models/order.js";
import Book from "../models/book.js";
import User from "../models/user.js";

export const createOrder = async (req, res) => {
  try {
    const {
      user,
      items,
      shippingAddress,
      paymentMethod,
      paymentStatus,
      orderStatus,
    } = req.body;

    if (!user || !items || items.length === 0 || !shippingAddress || !paymentMethod) {
      return res.status(400).json({ error: "All required fields must be filled" });
    }

    // Calculate total price from items
    let totalPrice = 0;

    for (const item of items) {
      const book = await Book.findById(item.book);
      if (!book) {
        return res.status(400).json({ error: `Book not found: ${item.book}` });
      }
      if (book.stock < item.quantity) {
        return res.status(400).json({ error: `Insufficient stock for book: ${book.title}` });
      }
      totalPrice += item.quantity * book.price;
    }

    const newOrder = new Order({
      user,
      items,
      shippingAddress,
      paymentMethod,
      paymentStatus: paymentStatus || "pending",
      orderStatus: orderStatus || "processing",
      totalPrice,
    });

    await newOrder.save();
    res.status(201).json({ message: "Order created successfully" });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getAllOrders = async (req, res) => {
  try {
    const orders = await Order.find()
      .populate("user", "name email")
      .populate("items.book", "title price");
    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrderById = async (req, res) => {
  const id = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid order ID" });
  }

  try {
    const order = await Order.findById(id)
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!order) {
      return res.status(404).json({ error: "Order not found" });
    }

    res.json(order);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const deleteOrder = async (req, res) => {
  const id = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid order ID" });
  }

  try {
    const deletedOrder = await Order.findByIdAndDelete(id);

    if (!deletedOrder) {
      return res.status(404).json({ error: "Order not found" });
    }

    res.json({ message: "Order deleted successfully" });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const updateOrder = async (req, res) => {
  const id = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid order ID" });
  }

  try {
    const updatedOrder = await Order.findByIdAndUpdate(id, req.body, {
      new: true,
    });

    if (!updatedOrder) {
      return res.status(404).json({ error: "Order not found" });
    }

    res.json({ message: "Order updated successfully", updatedOrder });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
//-----------------------------------------------------------------------------------------------------

export const updateOrderStatus = async (req, res) => {
  const id = req.params.id;
  const { orderStatus } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return res.status(400).json({ error: "Invalid order ID" });
  }

  try {
    const updatedOrder = await Order.findByIdAndUpdate(
      id,
      { orderStatus },
      { new: true }
    );

    if (!updatedOrder) {
      return res.status(404).json({ error: "Order not found" });
    }

    res.json({ message: "Order status updated successfully", updatedOrder });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
export const getUserOrders = async (req, res) => {
  const userId = req.params.userId;

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({ error: "Invalid user ID" });
  }

  try {
    const orders = await Order.find({ user: userId })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found for this user" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
export const getOrderByUserId = async (req, res) => {
  const userId = req.params.userId;

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({ error: "Invalid user ID" });
  }

  try {
    const orders = await Order.find({ user: userId })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found for this user" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
export const getOrderByBookId = async (req, res) => {
  const bookId = req.params.bookId;

  if (!mongoose.Types.ObjectId.isValid(bookId)) {
    return res.status(400).json({ error: "Invalid book ID" });
  }

  try {
    const orders = await Order.find({ "items.book": bookId })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found for this book" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
export const getOrderByPaymentStatus = async (req, res) => {
  const paymentStatus = req.params.paymentStatus;

  try {
    const orders = await Order.find({ paymentStatus })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this payment status" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};
export const getOrderByShippingAddress = async (req, res) => {
  const shippingAddress = req.params.shippingAddress;

  try {
    const orders = await Order.find({ shippingAddress })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this shipping address" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByBookId = async (req, res) => {
  const bookId = req.params.bookId;

  if (!mongoose.Types.ObjectId.isValid(bookId)) {
    return res.status(400).json({ error: "Invalid book ID" });
  }

  try {
    const orders = await Order.find({ "items.book": bookId })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found for this book" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByStatus = async (req, res) => {
  const status = req.params.status;

  try {
    const orders = await Order.find({ orderStatus: status })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this status" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByDateRange = async (req, res) => {
  const { startDate, endDate } = req.query;

  try {
    const orders = await Order.find({
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found in this date range" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByPaymentMethod = async (req, res) => {
  const method = req.params.method;

  try {
    const orders = await Order.find({ paymentMethod: method })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this payment method" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByShippingAddress = async (req, res) => {
  const address = req.params.address;

  try {
    const orders = await Order.find({ shippingAddress: address })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this shipping address" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByTotalAmount = async (req, res) => {
  const amount = parseFloat(req.params.amount);

  if (isNaN(amount)) {
    return res.status(400).json({ error: "Invalid amount" });
  }

  try {
    const orders = await Order.find({ totalPrice: amount })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this total amount" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};

export const getOrdersByTrackingNumber = async (req, res) => {
  const trackingNumber = req.params.trackingNumber;

  try {
    const orders = await Order.find({ trackingNumber })
      .populate("user", "name email")
      .populate("items.book", "title price");

    if (!orders || orders.length === 0) {
      return res.status(404).json({ error: "No orders found with this tracking number" });
    }

    res.json(orders);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: err.message });
  }
};